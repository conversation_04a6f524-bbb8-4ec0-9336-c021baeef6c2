# PowerShell setup script for Elevate3D on Windows
Write-Host "Setting up Elevate3D project for Windows..." -ForegroundColor Green
Write-Host ""

# Check if aria2c is available
try {
    $null = Get-Command aria2c -ErrorAction Stop
    Write-Host "✓ aria2c found" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: aria2c is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install aria2c first:" -ForegroundColor Yellow
    Write-Host "  Option 1: winget install aria2.aria2" -ForegroundColor Yellow
    Write-Host "  Option 2: Download from https://aria2.github.io/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create necessary directories
Write-Host "Creating directory structure..." -ForegroundColor Blue
$directories = @(
    "Checkpoints\sam",
    "PoissonRecon\Bin\Windows",
    "Inputs"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  Created: $dir" -ForegroundColo<PERSON> Gray
    } else {
        Write-Host "  Exists: $dir" -ForegroundColor Gray
    }
}

# Download SAM checkpoint
Write-Host ""
Write-Host "Downloading SAM checkpoint..." -ForegroundColor Blue
$samPath = "Checkpoints\sam\sam_vit_h_4b8939.pth"
if (!(Test-Path $samPath)) {
    & aria2c -x 8 -s 8 -d "Checkpoints\sam" -o "sam_vit_h_4b8939.pth" "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: Failed to download SAM checkpoint" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "✓ SAM checkpoint downloaded" -ForegroundColor Green
} else {
    Write-Host "✓ SAM checkpoint already exists" -ForegroundColor Green
}

# Download PoissonRecon prebuilt executables
Write-Host ""
Write-Host "Downloading PoissonRecon prebuilt executables..." -ForegroundColor Blue
$poissonExe = "PoissonRecon\Bin\Windows\PoissonRecon.exe"
if (!(Test-Path $poissonExe)) {
    & aria2c -x 8 -s 8 -d "." -o "AdaptiveSolvers.x64.zip" "https://www.cs.jhu.edu/~misha/Code/PoissonRecon/Version18.74/AdaptiveSolvers.x64.zip"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: Failed to download PoissonRecon executables" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }

    # Extract PoissonRecon executables
    Write-Host "Extracting PoissonRecon executables..." -ForegroundColor Blue
    try {
        Expand-Archive -Path "AdaptiveSolvers.x64.zip" -DestinationPath "temp_extract" -Force
        
        # Move executables to correct location
        Write-Host "Moving executables to PoissonRecon\Bin\Windows\..." -ForegroundColor Blue
        Get-ChildItem "temp_extract\AdaptiveSolvers.x64\*" | Move-Item -Destination "PoissonRecon\Bin\Windows\" -Force
        
        # Clean up
        Remove-Item "temp_extract" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item "AdaptiveSolvers.x64.zip" -Force -ErrorAction SilentlyContinue
        
        Write-Host "✓ PoissonRecon executables installed" -ForegroundColor Green
    } catch {
        Write-Host "✗ ERROR: Failed to extract PoissonRecon executables: $_" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "✓ PoissonRecon executables already exist" -ForegroundColor Green
}

# Download example data from Google Drive
Write-Host ""
Write-Host "Downloading example data from Google Drive..." -ForegroundColor Blue

# Google Drive direct download URL (converted from share link)
$googleDriveFileId = "1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya"
$googleDriveUrl = "https://drive.google.com/uc?export=download&id=$googleDriveFileId"

if (!(Test-Path "Inputs\2D") -or !(Test-Path "Inputs\3D")) {
    try {
        # Try to download with aria2c first
        Write-Host "Attempting download with aria2c..." -ForegroundColor Gray
        & aria2c -x 8 -s 8 -d "." -o "Inputs.zip" $googleDriveUrl

        if ($LASTEXITCODE -ne 0) {
            # Fallback to curl if aria2c fails
            Write-Host "aria2c failed, trying with curl..." -ForegroundColor Gray
            & curl -L -o "Inputs.zip" $googleDriveUrl

            if ($LASTEXITCODE -ne 0) {
                # Fallback to PowerShell Invoke-WebRequest
                Write-Host "curl failed, trying with PowerShell..." -ForegroundColor Gray
                Invoke-WebRequest -Uri $googleDriveUrl -OutFile "Inputs.zip" -UserAgent "Mozilla/5.0"
            }
        }

        if (Test-Path "Inputs.zip") {
            # Check if it's actually a zip file (Google Drive sometimes returns HTML)
            try {
                $fileHeader = Get-Content "Inputs.zip" -Head 1
                if ($fileHeader -like "*<html*" -or $fileHeader -like "*<!DOCTYPE*") {
                    Write-Host "⚠ Google Drive returned HTML instead of file. Manual download required." -ForegroundColor Yellow
                    Remove-Item "Inputs.zip" -Force
                    throw "Google Drive anti-bot protection triggered"
                }
            } catch {
                # If we can't read the header, assume it's a binary zip file (which is good)
                Write-Host "File appears to be binary (likely a valid zip file)" -ForegroundColor Gray
            }

            # Extract the zip file
            Write-Host "Extracting Inputs.zip..." -ForegroundColor Blue
            try {
                Expand-Archive -Path "Inputs.zip" -DestinationPath "." -Force
                Remove-Item "Inputs.zip" -Force
                Write-Host "✓ Example data extracted" -ForegroundColor Green
            } catch {
                Write-Host "⚠ Failed to extract zip file: $_" -ForegroundColor Yellow
                Write-Host "The file may be corrupted or not a valid zip. Please download manually." -ForegroundColor Yellow
                throw "Extraction failed"
            }
        } else {
            throw "Download failed"
        }
    } catch {
        Write-Host "⚠ Automatic download failed: $_" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Please download manually:" -ForegroundColor Yellow
        Write-Host "  1. Go to: https://drive.google.com/file/d/1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya/view?usp=sharing" -ForegroundColor Cyan
        Write-Host "  2. Download Inputs.zip" -ForegroundColor White
        Write-Host "  3. Extract it to this directory" -ForegroundColor White
        Write-Host ""
        $response = Read-Host "Have you downloaded and extracted Inputs.zip manually? (y/n)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "Please complete the manual download and run this script again." -ForegroundColor Yellow
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
} else {
    Write-Host "✓ Example data already exists" -ForegroundColor Green
}

# Update config files for Windows
Write-Host ""
Write-Host "Updating configuration files for Windows..." -ForegroundColor Blue

$configFiles = @(
    "Configs\config_gso.yaml",
    "Configs\config_trellis.yaml"
)

$linuxPath = "./PoissonRecon/Bin/Linux/PoissonRecon"
$windowsPath = "./PoissonRecon/Bin/Windows/PoissonRecon.exe"

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        try {
            $content = Get-Content $configFile -Raw
            $updatedContent = $content -replace [regex]::Escape($linuxPath), $windowsPath
            Set-Content -Path $configFile -Value $updatedContent -NoNewline
            Write-Host "  ✓ Updated $configFile" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ Failed to update $configFile" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠ $configFile not found" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Complete setup finished successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "✅ What was set up:" -ForegroundColor Yellow
Write-Host "  ✓ SAM checkpoint downloaded" -ForegroundColor Gray
Write-Host "  ✓ PoissonRecon Windows executables installed" -ForegroundColor Gray
Write-Host "  ✓ Example data downloaded and extracted" -ForegroundColor Gray
Write-Host "  ✓ Config files updated for Windows paths" -ForegroundColor Gray
Write-Host ""
Write-Host "🚀 Next steps to start using Elevate3D:" -ForegroundColor Yellow
Write-Host "  1. Create conda environment: conda env create -f environment.yml --name elevate3d" -ForegroundColor White
Write-Host "  2. Activate environment: conda activate elevate3d" -ForegroundColor White
Write-Host "  3. Test the setup: .\PoissonRecon\Bin\Windows\PoissonRecon.exe --help" -ForegroundColor White
Write-Host "  4. Run examples: python -m FLUX.flux_HFS-SDEdit" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to continue"
