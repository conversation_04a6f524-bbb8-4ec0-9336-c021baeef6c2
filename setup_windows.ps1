# PowerShell setup script for Elevate3D on Windows
Write-Host "Setting up Elevate3D project for Windows..." -ForegroundColor Green
Write-Host ""

# Check if aria2c is available
try {
    $null = Get-Command aria2c -ErrorAction Stop
    Write-Host "✓ aria2c found" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: aria2c is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install aria2c first:" -ForegroundColor Yellow
    Write-Host "  Option 1: winget install aria2.aria2" -ForegroundColor Yellow
    Write-Host "  Option 2: Download from https://aria2.github.io/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create necessary directories
Write-Host "Creating directory structure..." -ForegroundColor Blue
$directories = @(
    "Checkpoints\sam",
    "PoissonRecon\Bin\Windows",
    "Inputs"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  Created: $dir" -ForegroundColo<PERSON> Gray
    } else {
        Write-Host "  Exists: $dir" -ForegroundColor Gray
    }
}

# Download SAM checkpoint
Write-Host ""
Write-Host "Downloading SAM checkpoint..." -ForegroundColor Blue
$samPath = "Checkpoints\sam\sam_vit_h_4b8939.pth"
if (!(Test-Path $samPath)) {
    & aria2c -x 8 -s 8 -d "Checkpoints\sam" -o "sam_vit_h_4b8939.pth" "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: Failed to download SAM checkpoint" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "✓ SAM checkpoint downloaded" -ForegroundColor Green
} else {
    Write-Host "✓ SAM checkpoint already exists" -ForegroundColor Green
}

# Download PoissonRecon prebuilt executables
Write-Host ""
Write-Host "Downloading PoissonRecon prebuilt executables..." -ForegroundColor Blue
$poissonExe = "PoissonRecon\Bin\Windows\PoissonRecon.exe"
if (!(Test-Path $poissonExe)) {
    & aria2c -x 8 -s 8 -d "." -o "AdaptiveSolvers.x64.zip" "https://www.cs.jhu.edu/~misha/Code/PoissonRecon/Version18.74/AdaptiveSolvers.x64.zip"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: Failed to download PoissonRecon executables" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }

    # Extract PoissonRecon executables
    Write-Host "Extracting PoissonRecon executables..." -ForegroundColor Blue
    try {
        Expand-Archive -Path "AdaptiveSolvers.x64.zip" -DestinationPath "temp_extract" -Force
        
        # Move executables to correct location
        Write-Host "Moving executables to PoissonRecon\Bin\Windows\..." -ForegroundColor Blue
        Get-ChildItem "temp_extract\AdaptiveSolvers.x64\*" | Move-Item -Destination "PoissonRecon\Bin\Windows\" -Force
        
        # Clean up
        Remove-Item "temp_extract" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item "AdaptiveSolvers.x64.zip" -Force -ErrorAction SilentlyContinue
        
        Write-Host "✓ PoissonRecon executables installed" -ForegroundColor Green
    } catch {
        Write-Host "✗ ERROR: Failed to extract PoissonRecon executables: $_" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "✓ PoissonRecon executables already exist" -ForegroundColor Green
}

Write-Host ""
Write-Host "Setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Directory structure created:" -ForegroundColor Yellow
Write-Host "  ✓ Checkpoints\sam\sam_vit_h_4b8939.pth" -ForegroundColor Gray
Write-Host "  ✓ PoissonRecon\Bin\Windows\PoissonRecon.exe (and other tools)" -ForegroundColor Gray
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "  1. Download Inputs.zip from Google Drive:" -ForegroundColor White
Write-Host "     https://drive.google.com/file/d/1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya/view?usp=sharing" -ForegroundColor Cyan
Write-Host "  2. Extract Inputs.zip to the project root directory" -ForegroundColor White
Write-Host "  3. Run: .\update_config_for_windows.ps1" -ForegroundColor White
Write-Host "  4. Create conda environment: conda env create -f environment.yml --name elevate3d" -ForegroundColor White
Write-Host "  5. Activate environment: conda activate elevate3d" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to continue"
