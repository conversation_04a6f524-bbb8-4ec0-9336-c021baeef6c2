# Elevate3D Windows Setup Commands

## Prerequisites
First, install aria2c if you don't have it:
```powershell
winget install aria2.aria2
```

## Quick Setup (Fully Automated)

### Option 1: Using the PowerShell script (Recommended)
```powershell
# Run the complete automated setup script (does everything!)
.\setup_windows.ps1
```

### Option 2: Using the Batch file
```cmd
# Run the complete automated setup script
.\setup_windows.bat
```

### Option 3: Manual commands using aria2c

```powershell
# Create directories
mkdir Checkpoints\sam, PoissonRecon\Bin\Windows, Inputs -Force

# Download SAM checkpoint
aria2c -x 8 -s 8 -d "Checkpoints\sam" -o "sam_vit_h_4b8939.pth" "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"

# Download PoissonRecon prebuilt executables
aria2c -x 8 -s 8 -d "." -o "AdaptiveSolvers.x64.zip" "https://www.cs.jhu.edu/~misha/Code/PoissonRecon/Version18.74/AdaptiveSolvers.x64.zip"

# Extract and move executables
Expand-Archive -Path "AdaptiveSolvers.x64.zip" -DestinationPath "temp_extract" -Force
Move-Item "temp_extract\AdaptiveSolvers.x64\*" "PoissonRecon\Bin\Windows\" -Force
Remove-Item "temp_extract" -Recurse -Force
Remove-Item "AdaptiveSolvers.x64.zip" -Force

# For Google Drive download, use the specialized script
.\download_gdrive_inputs.ps1

# Update config files
(Get-Content "Configs\config_gso.yaml") -replace "./PoissonRecon/Bin/Linux/PoissonRecon", "./PoissonRecon/Bin/Windows/PoissonRecon.exe" | Set-Content "Configs\config_gso.yaml"
(Get-Content "Configs\config_trellis.yaml") -replace "./PoissonRecon/Bin/Linux/PoissonRecon", "./PoissonRecon/Bin/Windows/PoissonRecon.exe" | Set-Content "Configs\config_trellis.yaml"
```

### Option 4: Google Drive Download Only

If you only need to download the example data:

```powershell
# Specialized Google Drive downloader
.\download_gdrive_inputs.ps1

# Or install gdown for better Google Drive support
pip install gdown
gdown --id 1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya --output Inputs.zip
```

## After Automated Setup

The setup scripts now handle everything automatically, including:
- ✅ SAM checkpoint download
- ✅ PoissonRecon Windows executables
- ✅ Example data download from Google Drive (with fallback to manual)
- ✅ Config file updates for Windows paths

**Next steps:**

1. **Set up conda environment**:

   ```powershell
   conda env create -f environment.yml --name elevate3d
   conda activate elevate3d
   ```

2. **Verify setup**:

   ```powershell
   # Check if PoissonRecon executable works
   .\PoissonRecon\Bin\Windows\PoissonRecon.exe --help
   ```

3. **Run examples**:

   ```powershell
   # Test 2D image refinement
   python -m FLUX.flux_HFS-SDEdit

   # Test full 3D model refinement
   bash run_3d_refine_script_gso_example.sh
   ```

## Final Directory Structure
After setup, your directory should look like:
```
Elevate3D/
├── Checkpoints/
│   └── sam/
│       └── sam_vit_h_4b8939.pth
├── Inputs/                     # After manual download
│   ├── 2D/
│   └── 3D/
├── PoissonRecon/
│   └── Bin/
│       └── Windows/
│           ├── PoissonRecon.exe
│           ├── SurfaceTrimmer.exe
│           └── ... (other tools)
└── ... (other project files)
```

## Troubleshooting

- **aria2c not found**: Install with `winget install aria2.aria2` or download from https://aria2.github.io/
- **PowerShell execution policy**: Run `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
- **Config files not updated**: Run `.\update_config_for_windows.ps1` manually
- **PoissonRecon.exe not working**: Make sure you're on a 64-bit Windows system

## Alternative: Using curl instead of aria2c

If you prefer curl (available in Windows 10+):
```powershell
# Download SAM checkpoint
curl -L -o "Checkpoints\sam\sam_vit_h_4b8939.pth" "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"

# Download PoissonRecon
curl -L -o "AdaptiveSolvers.x64.zip" "https://www.cs.jhu.edu/~misha/Code/PoissonRecon/Version18.74/AdaptiveSolvers.x64.zip"
```
