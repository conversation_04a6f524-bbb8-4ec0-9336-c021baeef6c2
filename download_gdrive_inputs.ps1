# Specialized Google Drive downloader for Elevate3D Inputs.zip
# This script handles Google Drive's authentication and confirmation flow

param(
    [string]$FileId = "1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya",
    [string]$OutputFile = "Inputs.zip"
)

Write-Host "Google Drive Downloader for Elevate3D" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""

# Check if file already exists and is valid
if (Test-Path $OutputFile) {
    $fileInfo = Get-Item $OutputFile
    if ($fileInfo.Length -gt 1MB) {
        Write-Host "✓ $OutputFile already exists and appears valid ($($fileInfo.Length) bytes)" -ForegroundColor Green
        $response = Read-Host "Do you want to re-download? (y/n)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "Using existing file." -ForegroundColor Gray
            exit 0
        }
    }
}

Write-Host "Downloading from Google Drive (File ID: $FileId)..." -ForegroundColor Blue
Write-Host ""

# Method 1: Try gdown (most reliable)
function Try-GdownDownload {
    param($FileId, $OutputFile)
    
    try {
        Write-Host "Method 1: Checking for gdown..." -ForegroundColor Cyan
        $null = Get-Command gdown -ErrorAction Stop
        Write-Host "✓ gdown found, attempting download..." -ForegroundColor Green
        
        & gdown --id $FileId --output $OutputFile --quiet
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path $OutputFile)) {
            $fileInfo = Get-Item $OutputFile
            if ($fileInfo.Length -gt 1MB) {
                Write-Host "✓ Download successful with gdown ($($fileInfo.Length) bytes)" -ForegroundColor Green
                return $true
            }
        }
        
        Write-Host "✗ gdown download failed or file too small" -ForegroundColor Red
        return $false
        
    } catch {
        Write-Host "✗ gdown not available" -ForegroundColor Yellow
        Write-Host "  Install with: pip install gdown" -ForegroundColor Gray
        return $false
    }
}

# Method 2: Curl with confirmation handling
function Try-CurlDownload {
    param($FileId, $OutputFile)
    
    try {
        Write-Host "Method 2: Using curl with confirmation handling..." -ForegroundColor Cyan
        
        # Step 1: Get the initial page to extract confirmation token
        $tempHtml = "temp_gdrive_$([System.Guid]::NewGuid().ToString('N')[0..7] -join '').html"
        $cookieFile = "cookies_$([System.Guid]::NewGuid().ToString('N')[0..7] -join '').txt"
        $initialUrl = "https://drive.google.com/uc?export=download&id=$FileId"
        
        Write-Host "  Getting confirmation page..." -ForegroundColor Gray
        & curl -c $cookieFile -s -L -o $tempHtml $initialUrl
        
        if (Test-Path $tempHtml) {
            $htmlContent = Get-Content $tempHtml -Raw -ErrorAction SilentlyContinue
            
            # Look for the download warning page and confirmation
            if ($htmlContent -match 'confirm=([^&"]+)' -or $htmlContent -match '/uc\?export=download&amp;confirm=([^&"]+)') {
                $confirmToken = $matches[1]
                Write-Host "  Found confirmation token: $confirmToken" -ForegroundColor Gray
                
                $confirmUrl = "https://drive.google.com/uc?export=download&confirm=$confirmToken&id=$FileId"
                Write-Host "  Downloading with confirmation..." -ForegroundColor Gray
                
                & curl -b $cookieFile -L -o $OutputFile $confirmUrl
                
            } elseif ($htmlContent -match 'Too many users have viewed or downloaded this file recently') {
                Write-Host "  ✗ Google Drive quota exceeded" -ForegroundColor Red
                return $false
                
            } else {
                Write-Host "  No confirmation needed, trying direct download..." -ForegroundColor Gray
                & curl -L -o $OutputFile $initialUrl
            }
            
            # Clean up temp files
            Remove-Item $tempHtml -Force -ErrorAction SilentlyContinue
            Remove-Item $cookieFile -Force -ErrorAction SilentlyContinue
            
            if (Test-Path $OutputFile) {
                $fileInfo = Get-Item $OutputFile
                if ($fileInfo.Length -gt 1MB) {
                    Write-Host "✓ Download successful with curl ($($fileInfo.Length) bytes)" -ForegroundColor Green
                    return $true
                } else {
                    Write-Host "✗ Downloaded file too small ($($fileInfo.Length) bytes)" -ForegroundColor Red
                    Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
                }
            }
        }
        
        return $false
        
    } catch {
        Write-Host "✗ Curl download failed: $_" -ForegroundColor Red
        return $false
    }
}

# Method 3: PowerShell with session handling
function Try-PowerShellDownload {
    param($FileId, $OutputFile)
    
    try {
        Write-Host "Method 3: Using PowerShell with session handling..." -ForegroundColor Cyan
        
        $session = New-Object Microsoft.PowerShell.Commands.WebRequestSession
        $initialUrl = "https://drive.google.com/uc?export=download&id=$FileId"
        
        Write-Host "  Getting initial response..." -ForegroundColor Gray
        $response = Invoke-WebRequest -Uri $initialUrl -WebSession $session -UserAgent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        
        # Check if we got a confirmation page
        if ($response.Content -match 'confirm=([^&"]+)') {
            $confirmToken = $matches[1]
            Write-Host "  Found confirmation token, downloading..." -ForegroundColor Gray
            
            $confirmUrl = "https://drive.google.com/uc?export=download&confirm=$confirmToken&id=$FileId"
            Invoke-WebRequest -Uri $confirmUrl -OutFile $OutputFile -WebSession $session -UserAgent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            
        } else {
            Write-Host "  Direct download attempt..." -ForegroundColor Gray
            Invoke-WebRequest -Uri $initialUrl -OutFile $OutputFile -UserAgent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        if (Test-Path $OutputFile) {
            $fileInfo = Get-Item $OutputFile
            if ($fileInfo.Length -gt 1MB) {
                Write-Host "✓ Download successful with PowerShell ($($fileInfo.Length) bytes)" -ForegroundColor Green
                return $true
            } else {
                Write-Host "✗ Downloaded file too small ($($fileInfo.Length) bytes)" -ForegroundColor Red
                Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
            }
        }
        
        return $false
        
    } catch {
        Write-Host "✗ PowerShell download failed: $_" -ForegroundColor Red
        return $false
    }
}

# Try each method in order
$success = $false

$success = Try-GdownDownload -FileId $FileId -OutputFile $OutputFile
if (-not $success) {
    $success = Try-CurlDownload -FileId $FileId -OutputFile $OutputFile
}
if (-not $success) {
    $success = Try-PowerShellDownload -FileId $FileId -OutputFile $OutputFile
}

Write-Host ""
if ($success) {
    Write-Host "🎉 Download completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "  1. Extract: Expand-Archive -Path '$OutputFile' -DestinationPath '.' -Force" -ForegroundColor White
    Write-Host "  2. Or run: .\complete_setup.ps1" -ForegroundColor White
} else {
    Write-Host "❌ All download methods failed." -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual download required:" -ForegroundColor Yellow
    Write-Host "  1. Open: https://drive.google.com/file/d/$FileId/view?usp=sharing" -ForegroundColor Cyan
    Write-Host "  2. Click 'Download' and save as '$OutputFile'" -ForegroundColor White
    Write-Host "  3. Run: .\complete_setup.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "For better Google Drive support, install gdown:" -ForegroundColor Yellow
    Write-Host "  pip install gdown" -ForegroundColor Gray
}

Write-Host ""
Read-Host "Press Enter to continue"
