--find-links https://data.pyg.org/whl/torch-2.5.1+cu121.html
absl-py==2.1.0
accelerate==0.34.0
addict==2.4.0
aiohappyeyeballs==2.4.0
aiohttp==3.10.5
aiosignal==1.3.1
antlr4-python3-runtime==4.9.3
asttokens==2.4.1
async-timeout==4.0.3
attrs==24.2.0
bitsandbytes==0.44.1
blinker==1.8.2
carvekit==4.1.2
certifi==2024.8.30
cfgv==3.4.0
chardet==5.2.0
charset-normalizer==3.3.2
clean-fid==0.1.35
click==8.1.7
clip-anytorch==2.6.0
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
compel==2.0.3
ConfigArgParse==1.7
contourpy==1.3.0
cupy-cuda12x==13.5.1
cycler==0.12.1
dash==2.18.0
dash-core-components==2.0.0
dash-html-components==2.0.0
dash-table==5.0.0
dctorch==0.1.2
decorator==5.1.1
decord==0.6.0
diffusers==0.32.1
distlib==0.3.9
docker-pycreds==0.4.0
einops==0.8.0
et-xmlfile==1.1.0
exceptiongroup==1.2.2
executing==2.1.0
facexlib==0.3.0
fastjsonschema==2.20.0
fastrlock==0.8.2
filelock==3.15.4
filterpy==1.4.5
Flask==3.0.3
flatbuffers==24.3.25
fonttools==4.53.1
frozenlist==1.4.1
fsspec==2024.9.0
ftfy==6.2.3
future==1.0.0
fvcore==0.1.5.post20221221
gitdb==4.0.11
GitPython==3.1.43
glcontext==3.0.0
grpcio==1.66.1
h5py==3.11.0
huggingface-hub==0.24.6
humanfriendly==10.0
icecream==2.1.0
identify==2.6.1
idna==3.8
imageio==2.35.1
imageio-ffmpeg==0.5.1
importlib_metadata==8.4.0
iniconfig==2.0.0
iopath==0.1.10
ipython==8.27.0
ipywidgets==8.1.5
itsdangerous==2.2.0
jedi==0.19.1
Jinja2==3.1.4
joblib==1.4.2
jsmin==3.0.1
jsonmerge==1.9.2
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter_core==5.7.2
jupyterlab_widgets==3.0.13
k-diffusion==0.1.1.post1
kiwisolver==1.4.7
kornia==0.7.3
kornia_rs==0.1.5
lazy_loader==0.4
lightning-utilities==0.11.7
llvmlite==0.43.0
lmdb==1.5.1
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
modelcards==0.1.6
moderngl==5.12.0
mpmath==1.3.0
multidict==6.0.5
nbformat==5.10.4
nerfacc==0.3.3
nest-asyncio==1.6.0
networkx==3.3
ninja==********
nodeenv==1.9.1
numba==0.60.0
numpy==1.26.4
nvdiffrast @ git+https://github.com/NVlabs/nvdiffrast.git@729261dc64c4241ea36efda84fbf532cc8b425b8
objloader==0.2.0
omegaconf==2.2.3
onnxruntime==1.19.2
open-clip-torch==2.24.0
open3d==0.18.0
openai-clip==1.0.1
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
packaging==24.1
pandas==2.2.2
parso==0.8.4
pexpect==4.9.0
Pillow==11.3.0
piq==0.8.0
platformdirs==4.2.2
plotly==5.24.0
pluggy==1.5.0
pooch==1.8.2
portalocker==2.10.1
pre_commit==4.0.1
prompt_toolkit==3.0.47
protobuf==5.28.0
psutil==6.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pybind11==2.13.5
PyGLM==2.7.3
Pygments==2.18.0
pyhocon==0.3.57
PyMatting==1.1.12
PyMCubes==0.1.6
pymeshlab==2023.12.post1
pyparsing==3.1.4
pyquaternion==0.9.9
pyransac3d==0.6.0
pysteps==1.12.0
pytest==8.3.3
python-dateutil==2.9.0.post0
pytorch-lightning==1.9.4
pytorch3d==0.7.8
pytz==2024.1
pyvista==0.44.2
PyYAML==6.0.2
referencing==0.35.1
regex==2024.7.24
rembg==2.0.59
requests==2.32.3
retrying==1.3.4
rich==13.8.0
rpds-py==0.20.0
safetensors==0.4.4
scikit-image==0.24.0
scikit-learn==1.5.1
scipy==1.14.1
scooby==0.10.0
seaborn==0.13.2
segment-anything==1.0
sentencepiece==0.2.0
sentry-sdk==2.13.0
setproctitle==1.3.3
six==1.16.0
smmap==5.0.1
stack-data==0.6.3
sympy==1.13.1
tabulate==0.9.0
tenacity==9.0.0
tensorboard==2.17.1
tensorboard-data-server==0.7.2
termcolor==2.4.0
threadpoolctl==3.5.0
tifffile==2024.8.30
timm==1.0.9
tinycudann @ git+https://github.com/NVlabs/tiny-cuda-nn/@c91138bcd4c6877c8d5e60e483c0581aafc70cce#subdirectory=bindings/torch
tokenizers==0.20.1
torchao==0.12.0
torch-scatter
tomli==2.0.2
tqdm==4.66.5
traitlets==5.14.3
trampoline==0.1.2
transformers==4.46.1
trimesh==3.9.8
typing_extensions==4.12.2
tzdata==2024.1
urllib3==2.2.2
virtualenv==20.27.0
vtk==9.3.1
wandb==0.17.8
wcwidth==0.2.13
Werkzeug==3.0.4
widgetsnbextension==4.0.13
xatlas==0.0.11
yacs==0.1.8
yapf==0.40.2
yarl==1.9.11
zipp==3.20.1
xformers==0.0.26.post1
